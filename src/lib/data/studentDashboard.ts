import { db } from '$lib/database';
import { sql } from 'kysely';
import type { StudentScoreEvolution, StudentCourseScore, StudentCourseEvolution } from '$lib/types';

/**
 * Fetches score evolution data for a specific student
 * @param studentCode Student code to get score evolution for
 * @returns Array of score evolution data or null if error
 */
export async function getStudentScoreEvolution(
	studentCode: string
): Promise<StudentScoreEvolution[] | null> {
	try {
		const result = await sql<StudentScoreEvolution>`
			SELECT * FROM get_student_score_evolution(${studentCode})
		`.execute(db);

		if (!result.rows || !Array.isArray(result.rows)) {
			return null;
		}

		return result.rows;
	} catch (error) {
		console.error('Error fetching student score evolution:', error);
		return null;
	}
}

/**
 * Fetches course scores for a specific student
 * @param studentCode Student code to get course scores for
 * @returns Array of course scores or null if error
 */
export async function getStudentCourseScores(
	studentCode: string
): Promise<StudentCourseScore[] | null> {
	try {
		const result = await sql<StudentCourseScore>`
			SELECT * FROM get_student_course_scores(${studentCode})
		`.execute(db);

		if (!result.rows || !Array.isArray(result.rows)) {
			return null;
		}

		return result.rows;
	} catch (error) {
		console.error('Error fetching student course scores:', error);
		return null;
	}
}

/**
 * Fetches course evolution data for a specific student
 * @param studentCode Student code to get course evolution for
 * @returns Array of course evolution data or null if error
 */
export async function getStudentCourseEvolution(
	studentCode: string
): Promise<StudentCourseEvolution[] | null> {
	try {
		const result = await sql<StudentCourseEvolution>`
			SELECT * FROM get_student_course_evolution(${studentCode})
		`.execute(db);

		if (!result.rows || !Array.isArray(result.rows)) {
			return null;
		}

		return result.rows;
	} catch (error) {
		console.error('Error fetching student course evolution:', error);
		return null;
	}
}
