import { sql } from 'kysely';
import type { Levels } from '$lib/types';

// Cache para almacenar niveles por usuario
const levelsCache = new Map<string, { data: Levels[]; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutos en milisegundos

/**
 * Obtiene los niveles disponibles para un usuario con caché para mejorar rendimiento
 * @param userID ID del usuario
 * @param forceRefresh Si es true, ignora la caché y obtiene datos frescos
 * @returns Array de niveles
 */
export async function getLevels(userID: string, forceRefresh = false): Promise<Levels[]> {
	try {
		// Si no hay forzado de actualización y existe caché válida, usarla
		if (!forceRefresh && levelsCache.has(userID)) {
			const cache = levelsCache.get(userID)!;
			const now = Date.now();

			// Si la caché no ha expirado, retornar datos en caché
			if (now - cache.timestamp < CACHE_TTL) {
				return cache.data;
			}
		}

		// Obtener datos frescos usando K<PERSON>ely
		// PostgreSQL array contains operator @>
		const levels: Levels[] = await db
			.selectFrom('levels')
			.select(['code', 'name', 'abr', 'created_at', 'users'])
			.where(sql<boolean>`users @> ${JSON.stringify([userID])}`)
			.execute();

		const result = levels;

		// Actualizar caché
		if (levels) {
			levelsCache.set(userID, {
				data: result,
				timestamp: Date.now()
			});
		}

		return result;
	} catch (error) {
		return [];
	}
}
