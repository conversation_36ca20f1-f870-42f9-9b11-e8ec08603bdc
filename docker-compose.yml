networks:
  nextya:

services:
  app:
    container_name: nextya_app
    build:
      context: .
      dockerfile: docker/app.dockerfile
      target: development
      args:
        - USER_ID=${USER_ID:-1000}
        - GROUP_ID=${GROUP_ID:-1000}
    ports:
      - '5173:5173'
    env_file:
      - .env.docker
    environment:
      - DB_HOST=postgres
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - DB_NAME=nextya
      - DB_PORT=5432
      - NODE_ENV=development
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production-2024
      - JWT_EXPIRES_IN=8h
    volumes:
      # Bind mount source code for hot reload
      - .:/app:cached
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - nextya
    restart: unless-stopped

  postgres:
    container_name: nextya_postgres
    image: postgres:14-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=nextya
      # Use C locale for Alpine compatibility
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init:/docker-entrypoint-initdb.d
    ports:
      - '5432:5432'
    networks:
      - nextya
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
