{"name": "nextapp", "private": true, "version": "1.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test": "npm run format && npm run lint && npm run check", "migrate": "tsx scripts/migrate.ts", "migrate:up": "tsx scripts/migrate.ts up", "migrate:down": "tsx scripts/migrate.ts down", "db:generate": "kysely-codegen --dialect postgres --url \"********************************************/nextya\" --out-file src/lib/database/types.ts"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/js": "^9.25.1", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.21.2", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@types/bcryptjs": "^3.0.0", "@types/cookie": "^1.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.31", "@types/pg": "^8.15.4", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.9.1", "globals": "^16.2.0", "kysely-codegen": "^0.18.5", "kysely-ctl": "^0.13.1", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "svelte": "^5.33.19", "svelte-check": "^4.2.1", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5"}, "dependencies": {"@tailwindcss/vite": "^4.1.10", "bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "cookie": "^1.0.2", "daisyui": "^5.0.43", "express": "^5.1.0", "fast-csv": "^5.0.2", "jsonwebtoken": "^9.0.2", "kysely": "^0.28.2", "lucide-svelte": "^0.513.0", "pg": "^8.16.0", "tailwindcss": "^4.1.10", "zod": "^3.25.51"}}